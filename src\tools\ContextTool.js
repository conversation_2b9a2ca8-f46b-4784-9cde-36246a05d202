/**
 * ContextTool - Analyzes and provides project context information
 * Discovers project structure, dependencies, and provides intelligent context
 */

import { ProjectAnalyzer } from '../context/ProjectAnalyzer.js';

export class ContextTool {
  constructor(config = {}) {
    this.config = {
      maxFiles: config.maxFiles || 1000,
      maxDepth: config.maxDepth || 10,
      includeContent: config.includeContent || false,
      ...config
    };
    
    this.projectAnalyzer = new ProjectAnalyzer(this.config);
    this.usageStats = {
      analysisCount: 0,
      errors: 0
    };
    this.enabled = true;
  }

  /**
   * Get tool schema for AI providers
   */
  getSchema() {
    return {
      type: 'object',
      description: 'Analyze project context, discover file structure, dependencies, and provide intelligent project insights.',
      parameters: {
        type: 'object',
        properties: {
          operation: {
            type: 'string',
            enum: ['analyze', 'refresh', 'search', 'summary'],
            description: 'The context operation to perform'
          },
          path: {
            type: 'string',
            description: 'Path to analyze (defaults to current working directory)'
          },
          options: {
            type: 'object',
            description: 'Analysis options',
            properties: {
              deep: { type: 'boolean', description: 'Perform deep analysis' },
              includeContent: { type: 'boolean', description: 'Include file content in analysis' },
              maxFiles: { type: 'number', description: 'Maximum number of files to analyze' },
              maxDepth: { type: 'number', description: 'Maximum directory depth' },
              fileTypes: { 
                type: 'array', 
                items: { type: 'string' },
                description: 'Specific file types to analyze' 
              },
              excludePatterns: {
                type: 'array',
                items: { type: 'string' },
                description: 'Patterns to exclude from analysis'
              }
            }
          }
        },
        required: ['operation']
      }
    };
  }

  /**
   * Execute context analysis operation
   */
  async execute(args, context = null) {
    if (!this.enabled) {
      throw new Error('Context tool is disabled');
    }

    const { operation, path: targetPath, options = {} } = args;

    try {
      let result;
      const analysisPath = targetPath || process.cwd();

      switch (operation) {
        case 'analyze':
          result = await this.analyzeProject(analysisPath, options);
          this.usageStats.analysisCount++;
          break;
          
        case 'refresh':
          result = await this.refreshContext(analysisPath, options);
          break;
          
        case 'search':
          result = await this.searchContext(analysisPath, options);
          break;
          
        case 'summary':
          result = await this.getContextSummary(analysisPath, options);
          break;
          
        default:
          throw new Error(`Unsupported context operation: ${operation}`);
      }

      return {
        success: true,
        operation,
        path: analysisPath,
        result,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.usageStats.errors++;
      return {
        success: false,
        operation,
        path: targetPath,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Analyze project structure and context
   */
  async analyzeProject(projectPath, options = {}) {
    const analysis = await this.projectAnalyzer.analyze(projectPath, {
      deep: options.deep || false,
      includeContent: options.includeContent || this.config.includeContent,
      maxFiles: options.maxFiles || this.config.maxFiles,
      maxDepth: options.maxDepth || this.config.maxDepth,
      fileTypes: options.fileTypes,
      excludePatterns: options.excludePatterns
    });

    return {
      ...analysis,
      insights: this.generateInsights(analysis),
      recommendations: this.generateRecommendations(analysis)
    };
  }

  /**
   * Refresh existing context
   */
  async refreshContext(projectPath, options = {}) {
    // Force a fresh analysis
    const analysis = await this.projectAnalyzer.analyze(projectPath, {
      ...options,
      forceRefresh: true
    });

    return {
      refreshed: true,
      ...analysis,
      insights: this.generateInsights(analysis)
    };
  }

  /**
   * Search within project context
   */
  async searchContext(projectPath, options = {}) {
    const { query, fileTypes, includeContent } = options;
    
    if (!query) {
      throw new Error('Search query is required');
    }

    const analysis = await this.projectAnalyzer.analyze(projectPath, {
      includeContent: includeContent || false
    });

    const results = this.searchInAnalysis(analysis, query, {
      fileTypes,
      caseSensitive: options.caseSensitive || false
    });

    return {
      query,
      totalResults: results.length,
      results: results.slice(0, 50) // Limit results
    };
  }

  /**
   * Get context summary
   */
  async getContextSummary(projectPath, options = {}) {
    const analysis = await this.projectAnalyzer.analyze(projectPath, options);
    
    return {
      projectType: analysis.projectType,
      totalFiles: analysis.files.length,
      totalDirectories: analysis.directories?.length || 0,
      dependencies: analysis.dependencies.length,
      fileTypes: this.getFileTypeDistribution(analysis.files),
      size: this.calculateProjectSize(analysis.files),
      lastModified: this.getLastModifiedDate(analysis.files),
      complexity: this.assessComplexity(analysis)
    };
  }

  /**
   * Generate project insights
   */
  generateInsights(analysis) {
    const insights = [];

    // Project type insights
    if (analysis.projectType) {
      insights.push({
        type: 'project_type',
        message: `This appears to be a ${analysis.projectType} project`,
        confidence: 0.8
      });
    }

    // File structure insights
    const fileCount = analysis.files.length;
    if (fileCount > 1000) {
      insights.push({
        type: 'large_project',
        message: `Large project with ${fileCount} files - consider using .gitignore patterns`,
        confidence: 0.9
      });
    }

    // Dependency insights
    if (analysis.dependencies.length > 50) {
      insights.push({
        type: 'many_dependencies',
        message: `Project has ${analysis.dependencies.length} dependencies - consider dependency audit`,
        confidence: 0.7
      });
    }

    // Technology stack insights
    const techStack = this.identifyTechStack(analysis);
    if (techStack.length > 0) {
      insights.push({
        type: 'tech_stack',
        message: `Technology stack: ${techStack.join(', ')}`,
        confidence: 0.8
      });
    }

    // Code quality insights
    const codeQuality = this.assessCodeQuality(analysis);
    if (codeQuality.issues.length > 0) {
      insights.push({
        type: 'code_quality',
        message: `Potential issues: ${codeQuality.issues.join(', ')}`,
        confidence: 0.6
      });
    }

    return insights;
  }

  /**
   * Generate recommendations
   */
  generateRecommendations(analysis) {
    const recommendations = [];

    // Missing important files
    const missingFiles = this.checkForMissingFiles(analysis);
    if (missingFiles.length > 0) {
      recommendations.push({
        type: 'missing_files',
        priority: 'medium',
        message: `Consider adding: ${missingFiles.join(', ')}`,
        action: 'create_files'
      });
    }

    // Security recommendations
    const securityIssues = this.checkSecurityIssues(analysis);
    if (securityIssues.length > 0) {
      recommendations.push({
        type: 'security',
        priority: 'high',
        message: `Security concerns: ${securityIssues.join(', ')}`,
        action: 'review_security'
      });
    }

    // Performance recommendations
    const perfIssues = this.checkPerformanceIssues(analysis);
    if (perfIssues.length > 0) {
      recommendations.push({
        type: 'performance',
        priority: 'low',
        message: `Performance suggestions: ${perfIssues.join(', ')}`,
        action: 'optimize'
      });
    }

    return recommendations;
  }

  /**
   * Search within analysis results
   */
  searchInAnalysis(analysis, query, options = {}) {
    const results = [];
    const searchQuery = options.caseSensitive ? query : query.toLowerCase();

    for (const file of analysis.files) {
      // Filter by file type if specified
      if (options.fileTypes && options.fileTypes.length > 0) {
        const ext = file.path.split('.').pop()?.toLowerCase();
        if (!options.fileTypes.includes(ext)) {
          continue;
        }
      }

      // Search in file path
      const filePath = options.caseSensitive ? file.path : file.path.toLowerCase();
      if (filePath.includes(searchQuery)) {
        results.push({
          type: 'file_path',
          file: file.path,
          match: 'File path contains query'
        });
      }

      // Search in file content if available
      if (file.content) {
        const content = options.caseSensitive ? file.content : file.content.toLowerCase();
        if (content.includes(searchQuery)) {
          const lines = file.content.split('\n');
          const matchingLines = lines
            .map((line, index) => ({ line, number: index + 1 }))
            .filter(({ line }) => {
              const searchLine = options.caseSensitive ? line : line.toLowerCase();
              return searchLine.includes(searchQuery);
            })
            .slice(0, 5); // Limit to 5 matches per file

          results.push({
            type: 'file_content',
            file: file.path,
            matches: matchingLines
          });
        }
      }
    }

    return results;
  }

  /**
   * Get file type distribution
   */
  getFileTypeDistribution(files) {
    const distribution = {};
    
    for (const file of files) {
      const ext = file.path.split('.').pop()?.toLowerCase() || 'no-extension';
      distribution[ext] = (distribution[ext] || 0) + 1;
    }

    return Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .reduce((obj, [ext, count]) => {
        obj[ext] = count;
        return obj;
      }, {});
  }

  /**
   * Calculate project size
   */
  calculateProjectSize(files) {
    const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
    
    return {
      bytes: totalSize,
      human: this.formatBytes(totalSize)
    };
  }

  /**
   * Get last modified date
   */
  getLastModifiedDate(files) {
    const dates = files
      .map(file => file.modified)
      .filter(date => date)
      .sort((a, b) => new Date(b) - new Date(a));

    return dates[0] || null;
  }

  /**
   * Assess project complexity
   */
  assessComplexity(analysis) {
    let score = 0;
    
    // File count factor
    score += Math.min(analysis.files.length / 100, 5);
    
    // Dependency factor
    score += Math.min(analysis.dependencies.length / 20, 3);
    
    // Directory depth factor
    const maxDepth = Math.max(...analysis.files.map(f => f.path.split('/').length));
    score += Math.min(maxDepth / 5, 2);

    if (score < 3) return 'low';
    if (score < 6) return 'medium';
    if (score < 9) return 'high';
    return 'very_high';
  }

  /**
   * Identify technology stack
   */
  identifyTechStack(analysis) {
    const stack = [];
    const files = analysis.files.map(f => f.path);
    
    // JavaScript/Node.js
    if (files.some(f => f.includes('package.json'))) {
      stack.push('Node.js');
    }
    
    // Python
    if (files.some(f => f.includes('requirements.txt') || f.includes('setup.py'))) {
      stack.push('Python');
    }
    
    // Java
    if (files.some(f => f.includes('pom.xml') || f.includes('build.gradle'))) {
      stack.push('Java');
    }
    
    // .NET
    if (files.some(f => f.endsWith('.csproj') || f.endsWith('.sln'))) {
      stack.push('.NET');
    }
    
    // React
    if (analysis.dependencies.some(d => d.includes('react'))) {
      stack.push('React');
    }
    
    // Vue
    if (analysis.dependencies.some(d => d.includes('vue'))) {
      stack.push('Vue.js');
    }

    return stack;
  }

  /**
   * Check for missing important files
   */
  checkForMissingFiles(analysis) {
    const missing = [];
    const files = analysis.files.map(f => f.path.toLowerCase());
    
    const importantFiles = [
      'readme.md',
      '.gitignore',
      'license',
      'contributing.md'
    ];
    
    for (const file of importantFiles) {
      if (!files.some(f => f.includes(file))) {
        missing.push(file);
      }
    }
    
    return missing;
  }

  /**
   * Check for security issues
   */
  checkSecurityIssues(analysis) {
    const issues = [];
    const files = analysis.files.map(f => f.path);
    
    // Check for exposed secrets
    const sensitiveFiles = ['.env', 'config.json', 'secrets.json'];
    for (const file of sensitiveFiles) {
      if (files.some(f => f.includes(file))) {
        issues.push(`Potential secrets in ${file}`);
      }
    }
    
    return issues;
  }

  /**
   * Check for performance issues
   */
  checkPerformanceIssues(analysis) {
    const issues = [];

    // Large number of files
    if (analysis.files.length > 5000) {
      issues.push('Very large project - consider modularization');
    }

    // Many dependencies
    if (analysis.dependencies.length > 100) {
      issues.push('Many dependencies - consider dependency cleanup');
    }

    return issues;
  }

  /**
   * Assess code quality based on project analysis
   */
  assessCodeQuality(analysis) {
    const issues = [];
    const warnings = [];
    const suggestions = [];

    // Check for missing important files
    const missingFiles = this.checkForMissingFiles(analysis);
    if (missingFiles.includes('readme.md')) {
      issues.push('Missing README.md documentation');
    }
    if (missingFiles.includes('.gitignore')) {
      warnings.push('Missing .gitignore file');
    }
    if (missingFiles.includes('license')) {
      warnings.push('Missing LICENSE file');
    }

    // Check file organization
    const fileTypes = this.getFileTypeDistribution(analysis.files);
    const hasTests = analysis.files.some(f =>
      f.path.includes('test') ||
      f.path.includes('spec') ||
      f.path.includes('__tests__')
    );

    if (!hasTests) {
      issues.push('No test files detected');
    }

    // Check for configuration files
    const hasConfig = analysis.files.some(f =>
      f.path.includes('config') ||
      f.path.includes('.env') ||
      f.path.includes('settings')
    );

    if (!hasConfig && analysis.projectType !== 'Static Website') {
      warnings.push('No configuration files detected');
    }

    // Check for documentation
    const docFiles = analysis.files.filter(f =>
      f.path.includes('doc') ||
      f.path.includes('README') ||
      f.extension === 'md'
    );

    if (docFiles.length < 2) {
      suggestions.push('Consider adding more documentation');
    }

    // Check for large files
    const largeFiles = analysis.files.filter(f => f.size > 1024 * 1024); // > 1MB
    if (largeFiles.length > 0) {
      warnings.push(`${largeFiles.length} large files detected (>1MB)`);
    }

    // Check for deep nesting
    const maxDepth = Math.max(...analysis.files.map(f => f.path.split('/').length));
    if (maxDepth > 8) {
      warnings.push('Deep directory nesting detected');
    }

    // Check for code duplication patterns
    const duplicateNames = this.findDuplicateFileNames(analysis.files);
    if (duplicateNames.length > 0) {
      suggestions.push('Potential duplicate file names found');
    }

    // Technology-specific checks
    if (analysis.projectType === 'Node.js') {
      this.assessNodeJSQuality(analysis, issues, warnings, suggestions);
    } else if (analysis.projectType === 'Python') {
      this.assessPythonQuality(analysis, issues, warnings, suggestions);
    }

    return {
      score: this.calculateQualityScore(issues, warnings, suggestions),
      issues,
      warnings,
      suggestions,
      summary: this.generateQualitySummary(issues, warnings, suggestions)
    };
  }

  /**
   * Assess Node.js specific code quality
   */
  assessNodeJSQuality(analysis, issues, warnings, suggestions) {
    // Check for package.json
    const packageJson = analysis.files.find(f => f.path === 'package.json');
    if (!packageJson) {
      issues.push('Missing package.json file');
      return;
    }

    // Check for lock files
    const hasLockFile = analysis.files.some(f =>
      f.path === 'package-lock.json' ||
      f.path === 'yarn.lock' ||
      f.path === 'pnpm-lock.yaml'
    );

    if (!hasLockFile) {
      warnings.push('No lock file found (package-lock.json, yarn.lock, etc.)');
    }

    // Check for ESLint/Prettier
    const hasLinting = analysis.files.some(f =>
      f.path.includes('.eslintrc') ||
      f.path.includes('.prettierrc') ||
      f.path === '.eslintrc.js' ||
      f.path === '.eslintrc.json'
    );

    if (!hasLinting) {
      suggestions.push('Consider adding ESLint/Prettier for code quality');
    }

    // Check for TypeScript
    const hasTypeScript = analysis.files.some(f =>
      f.extension === 'ts' ||
      f.path === 'tsconfig.json'
    );

    if (hasTypeScript && !analysis.files.some(f => f.path === 'tsconfig.json')) {
      warnings.push('TypeScript files found but no tsconfig.json');
    }
  }

  /**
   * Assess Python specific code quality
   */
  assessPythonQuality(analysis, issues, warnings, suggestions) {
    // Check for requirements.txt or setup.py
    const hasDependencyFile = analysis.files.some(f =>
      f.path === 'requirements.txt' ||
      f.path === 'setup.py' ||
      f.path === 'pyproject.toml' ||
      f.path === 'Pipfile'
    );

    if (!hasDependencyFile) {
      warnings.push('No dependency file found (requirements.txt, setup.py, etc.)');
    }

    // Check for virtual environment
    const hasVenv = analysis.directories?.some(d =>
      d.name === 'venv' ||
      d.name === '.venv' ||
      d.name === 'env'
    );

    if (!hasVenv) {
      suggestions.push('Consider using virtual environment');
    }

    // Check for __init__.py files
    const pythonDirs = analysis.directories?.filter(d =>
      analysis.files.some(f => f.path.startsWith(d.path) && f.extension === 'py')
    ) || [];

    const missingInit = pythonDirs.filter(d =>
      !analysis.files.some(f => f.path === `${d.path}/__init__.py`)
    );

    if (missingInit.length > 0) {
      suggestions.push('Some Python packages missing __init__.py files');
    }
  }

  /**
   * Find duplicate file names
   */
  findDuplicateFileNames(files) {
    const nameCount = {};

    files.forEach(file => {
      const name = file.path.split('/').pop();
      nameCount[name] = (nameCount[name] || 0) + 1;
    });

    return Object.keys(nameCount).filter(name => nameCount[name] > 1);
  }

  /**
   * Calculate quality score
   */
  calculateQualityScore(issues, warnings, suggestions) {
    let score = 100;

    // Deduct points for issues
    score -= issues.length * 15;
    score -= warnings.length * 8;
    score -= suggestions.length * 3;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate quality summary
   */
  generateQualitySummary(issues, warnings, suggestions) {
    const total = issues.length + warnings.length + suggestions.length;

    if (total === 0) {
      return 'Excellent code quality - no issues detected';
    } else if (issues.length === 0 && warnings.length <= 2) {
      return 'Good code quality with minor improvements possible';
    } else if (issues.length <= 2) {
      return 'Moderate code quality - some issues need attention';
    } else {
      return 'Code quality needs improvement - multiple issues detected';
    }
  }

  /**
   * Format bytes to human readable
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Standard tool interface methods
  getDescription() {
    return 'Analyze project context and provide intelligent insights';
  }

  getCategory() {
    return 'analysis';
  }

  getCapabilities() {
    return ['project_analysis', 'context_discovery', 'file_search', 'insights'];
  }

  isAvailable() {
    return this.enabled;
  }

  getUsageStats() {
    return { ...this.usageStats };
  }

  async healthCheck() {
    try {
      await this.projectAnalyzer.healthCheck();
      return { status: 'healthy' };
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }

  setEnabled(enabled) {
    this.enabled = enabled;
  }

  getExamples() {
    return [
      { description: 'Analyze current project', operation: 'analyze' },
      { description: 'Search for files', operation: 'search', options: { query: 'component' } }
    ];
  }

  getUsageInstructions() {
    return 'Context analysis tool for project discovery and insights';
  }

  async updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  async cleanup() {
    // No persistent resources to clean up
  }
}
